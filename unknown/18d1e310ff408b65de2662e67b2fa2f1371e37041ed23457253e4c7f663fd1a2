<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Command, CommandGroup, CommandInputSearch, CommandItem, CommandList } from '@/components/ui/command';
import axios from 'axios';
import { ref, watch } from 'vue';

interface Status {
    value: string | number;
    label: string | string[];
}

interface Props {
    modelValue?: Status[];
    labelKeywords: string[] | string;
    millisecond?: number;
    length?: number;
    required?: boolean;
    api: string;
    iconPosition?: string;
    model?: string;
    showSearch?: boolean;
    defaultSearchValue?: string;
    icons?: string[] | string;
}

const props = withDefaults(defineProps<Props>(), {
    millisecond: 5,
    length: 2,
    required: true,
    iconPosition: 'left',
    model: 'array',
    showSearch: true,
});

const query = ref('');
const isOpen = ref(false);
const skipNextWatch = ref(false);
const results = ref<Status[]>(props.modelValue || []);
let debounceTimer: ReturnType<typeof setTimeout> | null = null;

const emit = defineEmits(['update:resultsUpdated', 'update:modelValue']);

async function fetchData(val: string) {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(async () => {
        if (val.length > props.length) {
            try {
                const response = await axios.get(props.api, {
                    params: { search: val }, // optional query param
                });
                results.value = response.data;
                isOpen.value = true;
            } catch (err) {
                console.error('API error:', err);
                isOpen.value = false;
            }
        } else {
            results.value = []; // Clear results if input is too short
            isOpen.value = false;
        }
    }, props.second! * 100);
}

function handleSelect(result: { value: string | number; label: string | number }) {
    if (props.model == 'array') {
        query.value = result.label[props.defaultSearchValue];
    } else if (props.showSearch == false) {
        query.value = '';
    } else {
        query.value = result.label;
    }
    isOpen.value = false;
    emit('resultsUpdated', result.value);
    skipNextWatch.value = true;
}

watch(query, (val) => {
    if (skipNextWatch.value) {
        skipNextWatch.value = false; // reset the flag
        return; // skip this watch
    }
    if (val.length <= 2) {
        results.value = [];
        isOpen.value = false;
        return;
    }
    fetchData(val);
});
</script>

<template>
    <Command class="!h-auto rounded-lg border shadow-md">
        <CommandInputSearch v-model="query" class="w-full" placeholder="Type a command or search..." :iconPosition="props.iconPosition" />
        <CommandList v-if="isOpen">
            <div v-if="results.length === 0" class="py-6 text-center text-sm" data-slot="command-empty">No results found.</div>
            <div v-else>
                <CommandGroup>
                    <CommandItem v-for="result in results" :key="result.value" :value="result.value" @select="handleSelect(result)">
                        <template v-if="model == 'array'">
                            <div class="grid w-full grid-cols-2 gap-4 lg:grid-cols-2">
                                <div
                                    v-for="(keyword, i) in labelKeywords"
                                    :key="i"
                                    :style="{ borderRight: i === keyword.length - 1 ? '' : '1px solid black' }"
                                >
                                    <FaIcon :name="props.icons[i]" />
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <FaIcon :name="props.icons" />
                            {{ result.label }}
                        </template>
                    </CommandItem>
                </CommandGroup>
            </div>
        </CommandList>
    </Command>
</template>
