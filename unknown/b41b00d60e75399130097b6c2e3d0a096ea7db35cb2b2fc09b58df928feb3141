<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InputSearchController extends Controller
{
    public function inputSearch(Request $request)
    {
        $search = $request->search;
        $matches = [];
        $api = [
            [
                'value' => 'backlog',
                'label' => [
                    'name' => 'Backlog',
                    'ic' => '1234567876543345678',
                    'contact' => '+60612345678',
                ],
            ],
            [
                'value' => 'todo',
                'label' => [
                    'name' => 'Todo',
                    'ic' => '098765432123456',
                    'contact' => '+60112345678',
                ],
            ],
            [
                'value' => 'in progress',
                'label' => [
                    'name' => 'In Progress',
                    'ic' => '008765432123456',
                    'contact' => '+60122345699',
                ],
            ],
            [
                'value' => 'done',
                'label' => [
                    'name' => 'Done',
                    'ic' => '988765432123456',
                    'contact' => '+601029495959',
                ],
            ],
            [
                'value' => 'canceled',
                'label' => 'Canceled',
            ],
        ];

        foreach ($api as $item) {
            if (is_array($item['label'])) {
                foreach ($item['label'] as $value) {
                    if (stripos($value, $search) !== false) {
                        $matches[] = $item;
                    }
                }
            } else {
                if (stripos($item['label'], $search) !== false) {
                    $matches[] = $item;
                }
            }
        }

        return $matches;
    }
}
