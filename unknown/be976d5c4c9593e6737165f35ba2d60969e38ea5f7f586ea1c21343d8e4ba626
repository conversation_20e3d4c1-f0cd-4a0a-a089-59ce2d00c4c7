<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('collateral_properties', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Relationships
            $table->foreignId('collateral_id')->constrained('collaterals')->cascadeOnDelete();

            // Property details
            $table->string('ownership_no')->nullable();
            $table->string('lot_number')->nullable();
            $table->foreignId('selection_land_category_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('land_category', 36)->nullable();
            $table->string('land_category_other')->nullable();
            $table->foreignId('selection_type_of_property_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('type_of_property')->nullable();
            $table->string('land_size', 36)->nullable();
            $table->foreignId('selection_land_size_unit')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('land_size_unit', 36)->nullable();
            $table->foreignId('selection_land_status_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('land_status', 36)->nullable();
            $table->string('city', 50)->nullable();
            $table->string('location')->nullable();
            $table->string('district')->nullable();

            // Additional property information
            $table->string('no_syit_piawai')->nullable();
            $table->string('certified_plan_no')->nullable();
            $table->string('built_up_area_of_property')->nullable();
            $table->foreignId('selection_built_up_area_unit')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('built_up_area_unit', 10)->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('collateral_properties');
    }
};
