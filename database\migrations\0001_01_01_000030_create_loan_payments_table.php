<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_payments', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('code', 32)->unique();

            // Foreign key relationships
            $table->foreignId('loan_txn_id')->constrained('loan_txns')->cascadeOnDelete();
            $table->foreignId('loan_txn_type_id')->constrained('loan_txn_types')->cascadeOnDelete();

            // Charge detail information
            $table->dateTime('txn_date');
            $table->string('txn_type', 72)->nullable();
            $table->string('payment_ref_code', 72)->nullable();
            $table->string('payment_method', 72);
            $table->dateTime('payment_date')->nullable();
            $table->decimal('amount', 24, 2);
            $table->decimal('rebate_amount', 24, 2)->nullable();
            $table->unsignedTinyInteger('status');
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_payments');
    }
};
