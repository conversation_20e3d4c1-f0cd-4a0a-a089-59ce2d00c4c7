<?php

use App\Http\Controllers\AccessControl\PermissionController;
use App\Http\Controllers\AccessControl\RoleController;
use App\Http\Controllers\Agents\AgentController;
use App\Http\Controllers\Agents\OutcomeController;
use App\Http\Controllers\Agents\OutcomeTypeController;
use App\Http\Controllers\Audits\AuditController;
use App\Http\Controllers\Collaterals\CollateralController;
use App\Http\Controllers\Companies\CompanyController;
use App\Http\Controllers\Companies\HeadquarterController;
use App\Http\Controllers\Customers\CustomerController;
use App\Http\Controllers\Loans\LoanController;
use App\Http\Controllers\Locales\LocaleController;
use App\Http\Controllers\Selections\SelectionController;
use App\Http\Controllers\Teams\TeamController;
use App\Http\Controllers\Users\UserController;
use App\Models\Headquarter;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get(
    '/',
    fn () => auth()->check()
        ? redirect()->route('dashboard')
        : redirect()->route('login')
)->name('home');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

// Explicit route model binding for Headquarter
Route::bind('headquarters', function ($value) {
    return Headquarter::findOrFail($value);
});

Route::middleware('auth')->group(function () {
    Route::get('dashboard', fn () => Inertia::render('Dashboard'))->name('dashboard');

    Route::resources([
        'roles' => RoleController::class,
        'permissions' => PermissionController::class,
        'users' => UserController::class,
        'headquarters' => HeadquarterController::class,
        'companies' => CompanyController::class,
        'teams' => TeamController::class,
        'agents' => AgentController::class,
        'agent-outcome-types' => OutcomeTypeController::class,
        'agent-outcomes' => OutcomeController::class,
        'locales' => LocaleController::class,
        'selections' => SelectionController::class,
        'collaterals' => CollateralController::class,
        'customers' => CustomerController::class,
        'loans' => LoanController::class,
    ]);

    Route::put('/users/{user}/update-status', [UserController::class, 'updateStatus'])->name('users.update-status');
    Route::put('/headquarters/{headquarters}/update-status', [HeadquarterController::class, 'updateStatus'])->name('headquarters.update-status');
    Route::put('/companies/{company}/update-status', [CompanyController::class, 'updateStatus'])->name('companies.update-status');
    Route::put('/teams/{team}/update-status', [TeamController::class, 'updateStatus'])->name('teams.update-status');
    Route::put('/agents/{agent}/update-status', [AgentController::class, 'updateStatus'])->name('agents.update-status');
    Route::put('/selections/{selection}/update-status', [SelectionController::class, 'updateStatus'])->name('selections.update-status');
    Route::put('/locales/{locale}/update-status', [LocaleController::class, 'updateStatus'])->name('locales.update-status');

    Route::get('audits', [AuditController::class, 'index'])->name('audits.index');
    Route::get('audits/{audit}', [AuditController::class, 'show'])->name('audits.show');

    Route::get('customer/search', [CustomerController::class, 'search'])->name('customer.search');
    Route::get('input-search-example', function () {
        return Inertia::render('component/InputSearch');
    });
});
