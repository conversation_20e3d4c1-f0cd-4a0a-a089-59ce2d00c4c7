<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

trait QueryFilterableTrait
{
    /**
     * Apply search filter to query
     */
    protected function applySearchFilter(Builder $query, Request $request, string $requestField = 'name', ?string $field = null): Builder
    {
        $field = $field ?? $requestField;
        $searchTerm = $request->input($requestField);

        if ($searchTerm) {
            $query->where(function ($q) use ($searchTerm, $field) {
                $q->where($field, 'like', "%{$searchTerm}%");
            });
        }

        return $query;
    }

    /**
     * Apply sorting to query
     */
    protected function applySorting(Builder $query, Request $request, string $defaultField = 'created_at', string $defaultDirection = 'desc'): Builder
    {
        $sortField = $request->input('sort_field', $defaultField);
        $sortDirection = $request->input('sort_direction', $defaultDirection);

        if (str_contains($sortField, ':')) {
            [$relation, $relationField] = explode(':', $sortField);
            $model = $query->getModel();
            $tableName = $model->getTable();

            // Handle special case for User model sorting by company display_name
            if ($model instanceof \App\Models\User && $relation === 'company') {
                // Join through adminProfiles -> companies relationship
                $query->leftJoin('userables', function ($join) use ($tableName) {
                    $join->on("{$tableName}.id", '=', 'userables.user_id')
                         ->where('userables.userable_type', '=', 'App\\Models\\AdminProfile');
                })
                ->leftJoin('admin_profiles', 'userables.userable_id', '=', 'admin_profiles.id')
                ->leftJoin('companyables', function ($join) {
                    $join->on('admin_profiles.id', '=', 'companyables.companyable_id')
                         ->where('companyables.companyable_type', '=', 'App\\Models\\AdminProfile');
                })
                ->leftJoin('companies', 'companyables.company_id', '=', 'companies.id')
                ->select("{$tableName}.*")
                ->orderBy("companies.{$relationField}", $sortDirection);

                return $query;
            }

            // Handle standard BelongsTo relationships
            if (method_exists($model, $relation) && $model->$relation() instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo) {
                $relationObj = $model->$relation();
                $foreignKey = $relationObj->getForeignKeyName();
                $relatedTable = $relationObj->getRelated()->getTable();

                if ($relatedTable === $tableName) {
                    $relatedTable = "{$relation}s";
                    $query->leftJoin("{$tableName} as {$relatedTable}", "{$tableName}.{$foreignKey}", '=', "{$relatedTable}.id");
                } else {
                    $query->leftJoin($relatedTable, "{$tableName}.{$foreignKey}", '=', "{$relatedTable}.id");
                }

                $query->select("{$tableName}.*")
                    ->orderBy("{$relatedTable}.{$relationField}", $sortDirection);
            } else {
                // Fallback to default sorting if relation is not supported
                $query->orderBy($defaultField, $sortDirection);
            }

            return $query;
        }

        return $query->orderBy($sortField, $sortDirection);
    }

    /**
     * Apply pagination to query
     */
    protected function applyPagination(Builder $query, Request $request, int $defaultPerPage = 10, ?callable $transformer = null): LengthAwarePaginator
    {
        $perPage = $request->input('per_page', $defaultPerPage);

        $paginator = $query->paginate($perPage)->withQueryString();

        if ($transformer) {
            return $paginator->through($transformer);
        }

        return $paginator;
    }

    /**
     * Apply status filter to query
     */
    protected function applyStatusFilter(Builder $query, Request $request, string $statusField = 'status'): Builder
    {
        return $query->when($request->filled($statusField), function ($query) use ($request, $statusField) {
            $query->where($statusField, $request->input($statusField));
        });
    }

    /**
     * Apply relation filter to query
     *
     * @param  Builder  $query  The query builder instance
     * @param  Request  $request  The request object
     * @param  string  $requestField  The request field to check
     * @param  string  $relation  The relation path to filter on
     * @param  string  $field  The field in the relation to filter
     * @param  string  $operator  The comparison operator (default: 'like')
     * @param  bool  $exactMatch  Whether to use exact matching or partial matching
     */
    protected function applyRelationFilter(
        Builder $query,
        Request $request,
        string $requestField,
        string $relation,
        string $field,
        string $operator = 'like',
        bool $exactMatch = false
    ): Builder {
        if ($request->filled($requestField)) {
            $value = $request->input($requestField);

            if ($operator === 'like' && ! $exactMatch) {
                $value = "%{$value}%";
            }

            $query->whereHas($relation, fn ($q) => $q->where($field, $operator, $value));
        }

        return $query;
    }

    /**
     * Apply nested relation filter to query
     *
     * @param  Builder  $query  The query builder instance
     * @param  Request  $request  The request object
     * @param  string  $requestField  The request field to check
     * @param  string  $modelType  The model type to filter on
     * @param  string  $relationPath  The relation path from model to target (dot notation)
     * @param  string  $field  The field in the final relation to filter
     * @param  array  $additionalFields  Additional fields to include in the OR condition
     */
    protected function applyNestedRelationFilter(
        Builder $query,
        Request $request,
        string $requestField,
        string $modelType,
        string $relationPath,
        string $field,
        array $additionalFields = []
    ): Builder {
        if ($request->filled($requestField)) {
            $value = $request->input($requestField);

            $query->whereHas('associatedModels', function ($query) use ($modelType, $relationPath, $field, $value, $additionalFields) {
                $query->where('model_type', $modelType)
                    ->whereHas('model', function ($query) use ($relationPath, $field, $value, $additionalFields) {
                        $relations = explode('.', $relationPath);
                        $lastRelation = array_pop($relations);

                        $nestedQuery = $query;
                        foreach ($relations as $relation) {
                            $nestedQuery = $nestedQuery->whereHas($relation, function ($q) {
                                return $q;
                            });
                        }

                        $nestedQuery->whereHas($lastRelation, function ($query) use ($field, $value, $additionalFields) {
                            $query->where($field, 'like', "%{$value}%");

                            foreach ($additionalFields as $additionalField) {
                                $query->orWhere($additionalField, 'like', "%{$value}%");
                            }
                        });
                    });
            });
        }

        return $query;
    }
}
