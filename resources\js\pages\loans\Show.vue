<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

interface Loan {
    id: number;
    uuid: string;
    code: string;
}

interface Props {
    loan: Loan;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Loan: ${props.loan.code}`" />

        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000013" description="View details of the loan record" />

            <AppCard title="View Loan" backRoute="loans.index" :form="form" :itemId="props.loan.id">
                <dl class="grid grid-cols-2 gap-x-8"></dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
