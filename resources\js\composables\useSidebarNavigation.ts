import { useAuth } from '@/composables/useAuth';
import { usePage } from '@inertiajs/vue3';
import { GalleryVerticalEnd } from 'lucide-vue-next';
import { computed } from 'vue';

export function useSidebarNavigation() {
    const page = usePage();
    const version = computed(() => page.props.version);
    const currentRoute = computed(() => page.url);
    const { user, hasPermission } = useAuth();

    const isRouteActive = (pattern: string) => {
        return currentRoute.value.startsWith(pattern);
    };

    const createNavItem = (title: string, url: string, icon: string, subItems: any[] = []) => {
        if (subItems.length === 0) {
            return {
                title,
                url,
                icon,
                isActive: url === '/dashboard' ? currentRoute.value === '/dashboard' : isRouteActive(url),
            };
        }

        const paths = subItems.map((item) => item.url).filter((url) => url !== '#');
        const isActive = paths.some((path) => isRouteActive(path));

        return {
            title,
            url,
            icon,
            isActive,
            items: subItems.map((item) => ({
                ...item,
                isActive: item.url !== '#' ? isRouteActive(item.url) : false,
            })),
        };
    };

    const userData = computed(() => ({
        user: {
            name: user.value?.username || 'Unknown User',
            email: user.value?.email || '',
            avatar: '',
        },
        teams: [
            {
                name: user.value?.roles[0] || 'Unknown Role',
                logo: GalleryVerticalEnd,
                version: version,
                lastLoginTime: user.value?.last_login_time || 'Unknown',
            },
        ],
    }));

    const navItems = computed(() => {
        const items = [
            createNavItem('Dashboard', '/dashboard', 'gauge-high'),

            createNavItem(
                'User Management',
                '#',
                'user-plus',
                [{ title: 'User', url: '/users', permission: 'read users' }].filter((item) => !item.permission || hasPermission(item.permission)),
            ),

            createNavItem(
                'Role Management',
                '#',
                'user-gear',
                [
                    { title: 'Headquarter', url: '/headquarters', permission: 'read headquarters' },
                    { title: 'Company', url: '/companies', permission: 'read companies' },
                    { title: 'Team', url: '/teams', permission: 'read teams' },
                    { title: 'Role', url: '/roles', permission: 'read roles' },
                ].filter((item) => !item.permission || hasPermission(item.permission)),
            ),

            createNavItem(
                'Agent Management',
                '#',
                'user-tie',
                [
                    { title: 'Agent', url: '/agents', permission: 'read agents' },
                    { title: 'Agent Outcome Type', url: '/agent-outcome-types', permission: 'read agent-outcome-types' },
                    { title: 'Agent Outcome', url: '/agent-outcomes', permission: 'read agent-outcomes' },
                ].filter((item) => !item.permission || hasPermission(item.permission)),
            ),

            createNavItem(
                'Collateral Management',
                '#',
                'house-chimney-user',
                [{ title: 'Collateral', url: '/collaterals', permission: 'read collaterals' }].filter(
                    (item) => !item.permission || hasPermission(item.permission),
                ),
            ),

            createNavItem(
                'Customer Management',
                '#',
                'user-group',
                [{ title: 'Customer', url: '/customers', permission: 'read customers' }].filter(
                    (item) => !item.permission || hasPermission(item.permission),
                ),
            ),

            createNavItem(
                'Loan Management',
                '#',
                'sack-dollar',
                [
                    { title: 'Loan', url: '/loans', permission: 'read loans' },
                    { title: 'Loan Return', url: '#', permission: 'read loans' },
                    { title: 'Loan Inquiry', url: '#', permission: 'read loans' },
                    { title: 'Inquiry Request', url: '#', permission: 'read loans' },
                ].filter((item) => !item.permission || hasPermission(item.permission)),
            ),

            createNavItem(
                'System Setting',
                '#',
                'screwdriver-wrench',
                [
                    { title: 'Selection', url: '/selections', permission: 'read selections' },
                    { title: 'Locale', url: '/locales', permission: 'read locales' },
                    { title: 'Audit', url: '/audits', permission: 'read audits' },
                    { title: 'Permission', url: '/permissions', permission: 'read permissions' },
                    // { title: 'System Setting Type', url: '#' },
                ].filter((item) => !item.permission || hasPermission(item.permission)),
            ),
        ];

        return items.filter((item) => !item.items || item.items.length > 0);
    });

    return {
        userData,
        navItems,
        isRouteActive,
    };
}
