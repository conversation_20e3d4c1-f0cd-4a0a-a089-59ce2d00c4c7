<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const { submitWithConfirmation } = useFormSubmit();

interface Loan {
    id: number;
    uuid: string;
    code: string;
}

interface Props {
    loan: Loan;
}

const props = defineProps<Props>();

const form = useForm({
    //
});

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('loans.update', props.loan.id),
            entityName: 'loan',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit Loan" />
        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000012" description="Edit the selected loan record" />

            <AppCard title="Edit Loan" :form="form" backRoute="loans.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2"></div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
