<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Button } from '@/components/ui/button';
import {
    CalendarCell,
    CalendarCellTrigger,
    CalendarGrid,
    CalendarGridBody,
    CalendarGridHead,
    CalendarGridRow,
    CalendarHeadCell,
    CalendarHeader,
    CalendarHeading,
} from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { type DateValue, getLocalTimeZone, parseDate, today } from '@internationalized/date';
import { CalendarRoot, type CalendarRootEmits, type CalendarRootProps, useDateFormatter, useForwardPropsEmits } from 'reka-ui';
import { createDecade, createYear, toDate } from 'reka-ui/date';
import { computed, type HTMLAttributes, onMounted, ref, watch } from 'vue';

const props = withDefaults(
    defineProps<
        CalendarRootProps & {
            class?: HTMLAttributes['class'];
            error?: string;
            placeholderLabel?: string;
            modelValue: any;
            disableFuture?: boolean;
            disableBeforeToday?: boolean;
        }
    >(),
    {
        placeholder() {
            return today(getLocalTimeZone());
        },
        weekdayFormat: 'short',
        disableFuture: false,
        disableBeforeToday: false,
    },
);
const emits = defineEmits<CalendarRootEmits & { 'update:modelValue': [value: string | null] }>();
const isOpen = ref(false);
const delegatedProps = computed(() => {
    const { class: _, placeholder: __, modelValue: ___, ...delegated } = props;
    return delegated;
});
const todayDate = today(getLocalTimeZone());

const internalValue = ref<DateValue | null>(null);

const initializeInternalValue = () => {
    if (!props.modelValue) {
        internalValue.value = null;
        return;
    }

    try {
        if (typeof props.modelValue === 'string') {
            internalValue.value = parseDate(props.modelValue);
            return;
        }

        if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
            const firstValue = props.modelValue[0];
            if (typeof firstValue === 'string') {
                internalValue.value = parseDate(firstValue);
                return;
            }
        }

        if (props.modelValue && typeof props.modelValue === 'object') {
            if ('year' in props.modelValue && 'month' in props.modelValue && 'day' in props.modelValue) {
                // Create a DateValue from the object properties
                const { year, month, day } = props.modelValue;
                // Convert to string format first
                const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                internalValue.value = parseDate(dateStr);
                return;
            }
        }

        // If we get here, we couldn't parse the input
        console.error('Unrecognized date format:', props.modelValue);
        internalValue.value = null;
    } catch (e) {
        console.error('Failed to parse date:', e);
        internalValue.value = null;
    }
};

// Initialize on component creation
onMounted(() => {
    initializeInternalValue();
});

// Watch for changes to the internal value and emit string format
watch(internalValue, (val) => {
    if (val) {
        try {
            const dateObj = val.toDate(getLocalTimeZone());
            const formattedDate = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`;
            emits('update:modelValue', formattedDate);
        } catch (e) {
            console.error('Error formatting date:', e);
            emits('update:modelValue', null);
        }
    } else {
        emits('update:modelValue', null);
    }
});

// Watch for external changes to modelValue
watch(
    () => props.modelValue,
    () => {
        initializeInternalValue();
    },
    { deep: true },
);

const forwarded = useForwardPropsEmits(delegatedProps, emits);

const formatter = useDateFormatter('en');
const hasSelected = ref(false);

const displayLabel = computed(() => {
    if (!hasSelected.value && props.placeholderLabel) {
        return props.placeholderLabel;
    }

    if (internalValue.value) {
        try {
            const dateObj = internalValue.value.toDate(getLocalTimeZone());
            return `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`;
        } catch (e) {
            console.error('Error displaying date:', e);
            return props.placeholderLabel || '';
        }
    }

    return props.placeholderLabel || '';
});

watch(internalValue, (val) => {
    if (val) {
        hasSelected.value = true;
        isOpen.value = false;
    }
});

const isDateDisabled = (date: DateValue) => {
    if (!date) return true;
    try {
        const isFutureDisabled = props.disableFuture && date.compare(todayDate) > 0;
        const isPastDisabled = props.disableBeforeToday && date.compare(todayDate) < 0;
        return isFutureDisabled || isPastDisabled;
    } catch (e) {
        console.error('Error checking if date is disabled:', e);
        return false;
    }
};
</script>

<template>
    <Popover v-model:open="isOpen">
        <PopoverTrigger as-child>
            <Button
                variant="outline"
                :class="
                    cn(
                        'w-full justify-between bg-white text-left font-normal',
                        props.placeholderLabel && !internalValue && 'text-muted-foreground',
                        props.error && 'border-red-500',
                    )
                "
            >
                {{ displayLabel }}
                <FaIcon name="calendar" />
            </Button>
        </PopoverTrigger>
        <PopoverContent class="w-auto p-0">
            <CalendarRoot
                v-slot="{ date, grid, weekDays }"
                :model-value="internalValue"
                @update:model-value="internalValue = $event"
                v-bind="forwarded"
                :class="cn('rounded-md border p-3', props.class)"
            >
                <CalendarHeader>
                    <CalendarHeading class="flex w-full items-center justify-between gap-2">
                        <Select
                            :default-value="internalValue ? internalValue.month.toString() : ''"
                            @update:model-value="
                                (v) => {
                                    if (!v || !internalValue) return;
                                    if (Number(v) === internalValue?.month) return;
                                    internalValue = internalValue.set({
                                        month: Number(v),
                                    });
                                }
                            "
                        >
                            <SelectTrigger aria-label="Month" class="w-[60%]">
                                <SelectValue placeholder="Month" />
                            </SelectTrigger>
                            <SelectContent class="max-h-[200px]">
                                <SelectItem v-for="month in createYear({ dateObj: date })" :key="month.toString()" :value="month.month.toString()">
                                    {{ formatter.custom(toDate(month), { month: 'long' }) }}
                                </SelectItem>
                            </SelectContent>
                        </Select>

                        <Select
                            :default-value="internalValue ? internalValue.year.toString() : ''"
                            @update:model-value="
                                (v) => {
                                    if (!v || !internalValue) return;
                                    if (Number(v) === internalValue?.year) return;
                                    internalValue = internalValue.set({
                                        year: Number(v),
                                    });
                                }
                            "
                        >
                            <SelectTrigger aria-label="Year" class="w-[40%]">
                                <SelectValue placeholder="Year" />
                            </SelectTrigger>
                            <SelectContent class="max-h-[200px]">
                                <SelectItem
                                    v-for="yearValue in createDecade({ dateObj: date, startIndex: -120, endIndex: 120 }).filter(
                                        (y) => y.year <= new Date().getFullYear(),
                                    )"
                                    :key="yearValue.toString()"
                                    :value="yearValue.year.toString()"
                                >
                                    {{ yearValue.year }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </CalendarHeading>
                </CalendarHeader>

                <div class="flex flex-col space-y-4 pt-4 sm:flex-row sm:gap-x-4 sm:gap-y-0">
                    <CalendarGrid v-for="month in grid" :key="month.value.toString()">
                        <CalendarGridHead>
                            <CalendarGridRow>
                                <CalendarHeadCell v-for="day in weekDays" :key="day">
                                    {{ day }}
                                </CalendarHeadCell>
                            </CalendarGridRow>
                        </CalendarGridHead>
                        <CalendarGridBody class="grid">
                            <CalendarGridRow v-for="(weekDates, index) in month.rows" :key="`weekDate-${index}`" class="mt-2 w-full">
                                <CalendarCell
                                    v-for="weekDate in weekDates"
                                    :key="weekDate.toString()"
                                    :date="weekDate"
                                    :disabled="isDateDisabled(weekDate)"
                                >
                                    <CalendarCellTrigger :day="weekDate" :month="month.value" :disabled="isDateDisabled(weekDate)" />
                                </CalendarCell>
                            </CalendarGridRow>
                        </CalendarGridBody>
                    </CalendarGrid>
                </div>
            </CalendarRoot>
        </PopoverContent>
    </Popover>
</template>
