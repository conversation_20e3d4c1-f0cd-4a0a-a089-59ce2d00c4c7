<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

interface Loan {
    id: number;
    uuid: string;
    code: string;
    updated_at: string;
    updated_by: {
        id: number;
        username: string;
    } | null;
}

interface Props {
    loans: PaginatedData<Loan>;
    filters: FilterOptions & {
        code?: string;
    };
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({});

const searchValue = ref(props.filters.code || '');

const debouncedSearch = useDebounceFn((filters: any) => {
    form.get(route('loans.index', filters), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
    });
}, 300);

const handleSearch = () => {
    debouncedSearch({
        code: searchValue.value,
    });
};

const handleReset = () => {
    searchValue.value = '';
    form.get(route('loans.index'));
};

const handleView = (loan: Loan) => {
    form.get(route('loans.show', loan.id));
};

const handleEdit = (loan: Loan) => {
    form.get(route('loans.edit', loan.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';

    form.get(
        route('loans.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const columns = [];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Loans" />

        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000010" />

            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Loan Code"
                searchPlaceholder="Search by loan code..."
                :status="false"
                @search="handleSearch"
                @reset="handleReset"
            />

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('loans.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Loan
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="loans.data"
                        :sort-state="sortState"
                        empty-message="No loans found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        :showDeleteButton="false"
                    >
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="loans.from" :to="loans.to" :total="loans.total" />
                            </div>
                            <div>
                                <Pagination :links="loans.links" @paginate="handlePaginate" />
                            </div>
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
