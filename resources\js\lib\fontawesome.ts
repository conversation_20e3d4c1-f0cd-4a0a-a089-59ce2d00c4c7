import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import type { App } from 'vue';

// Import icons by category
import {
    // Buttons
    faAngleLeft,
    faArrowRotateRight,
    // Interface
    faBars,
    // Agent Management
    faBook,
    faBuilding,
    faCalendar,
    // Status
    faCheck,
    faChevronLeft,
    // Navigation
    faChevronRight,
    faCircleUser,
    faClipboardCheck,
    faCloudArrowUp,
    faEdit,
    faEnvelope,
    faExclamation,
    faEye,
    faFile,
    faFileContract,
    faFilePdf,
    // Dashboard
    faGaugeHigh,
    // Collateral Management
    faGear,
    faHouseChimneyUser,
    faIdCard,
    faInfo,
    faListCheck,
    faMagnifyingGlass,
    faMinus,
    faPaperPlane,
    faPen,
    faPlus,
    // Role Management
    faRobot,
    // Loan Management
    faSackDollar,
    // Actions
    faSave,
    //System Management
    faScrewdriverWrench,
    //Sort
    faSort,
    faSortDown,
    faSortUp,
    faTerminal,
    faTimes,
    faTrash,
    // User Management
    faUser,
    faUserGear,
    faUserGroup,
    faUserPlus,
    faUsers,
    faUserSlash,
    faUserTie,
} from '@fortawesome/free-solid-svg-icons';

// Add all icons to library
library.add(
    faGaugeHigh,
    faUser,
    faUsers,
    faUserGroup,
    faUserPlus,
    faUserGear,
    faHouseChimneyUser,
    faSackDollar,
    faScrewdriverWrench,
    faTerminal,
    faRobot,
    faBuilding,
    faUserTie,
    faBook,
    faListCheck,
    faClipboardCheck,
    faGear,
    faFileContract,
    faChevronRight,
    faChevronLeft,
    faBars,
    faTimes,
    faEnvelope,
    faSave,
    faTrash,
    faEdit,
    faCheck,
    faExclamation,
    faInfo,
    faPaperPlane,
    faAngleLeft,
    faArrowRotateRight,
    faMagnifyingGlass,
    faPlus,
    faUserSlash,
    faEye,
    faPen,
    faSort,
    faSortDown,
    faSortUp,
    faMinus,
    faCalendar,
    faFile,
    faCloudArrowUp,
    faFilePdf,
    faIdCard,
    faCircleUser,
);

// Create a Vue plugin
export function installFontAwesome(app: App) {
    app.component('FontAwesomeIcon', FontAwesomeIcon);
}

// Export the component for direct use
export { FontAwesomeIcon };
