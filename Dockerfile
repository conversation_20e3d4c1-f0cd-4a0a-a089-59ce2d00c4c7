FROM bitnami/php-fpm:8.4.6-debian-12-r2 AS composer-build
WORKDIR /app
COPY . ./
RUN install_packages git unzip
RUN composer install --no-interaction --prefer-dist --optimize-autoloader

FROM node:22 AS node-build
WORKDIR /app
COPY --from=composer-build /app /app
RUN npm install
RUN npm run build

FROM bitnami/php-fpm:8.4.6-debian-12-r2
RUN install_packages \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    zip \
    curl \
    unzip \
    supervisor \
    bash \
    nginx && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /var/www
COPY --chown=daemon:daemon --from=composer-build /app /var/www/.
COPY --chown=daemon:daemon --from=node-build /app/public /var/www/public/
COPY ./docker/nginx.conf /etc/nginx/conf.d/default.conf
COPY ./docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

RUN printf "\nclear_env = no\npm.status_path = /status\n" >> /opt/bitnami/php/etc/php-fpm.d/www.conf
RUN rm -f /etc/nginx/sites-enabled/default /etc/nginx/sites-available/default

EXPOSE 80
CMD ["/usr/bin/supervisord"]
