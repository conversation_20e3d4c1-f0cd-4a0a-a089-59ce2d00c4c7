<script setup lang="ts">
interface Props {
    title: string;
    description?: string;
    pageNumber?: string;
}

defineProps<Props>();
</script>

<template>
    <div class="mb-2 space-y-0.5">
        <div class="flex items-baseline gap-4">
            <h2 class="text-xl font-semibold tracking-tight">{{ title }}</h2>

            <!-- pageNumber beside h2 -->
            <p v-if="pageNumber" class="text-muted-foreground text-sm">
                {{ pageNumber }}
            </p>
        </div>

        <!-- description below h2 and pageNumber -->
        <p v-if="description" class="text-muted-foreground text-sm">
            {{ description }}
        </p>
    </div>
</template>
