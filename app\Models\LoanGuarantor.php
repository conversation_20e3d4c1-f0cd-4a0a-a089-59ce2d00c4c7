<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * LoanGuarantor model for managing loan guarantor information
 */
class LoanGuarantor extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_id',
        'name',
        'identity_no',
        'age',
        'birth_date',
        'selection_gender_id',
        'gender',
        'selection_relationship_id',
        'relationship',
        'selection_race_id',
        'race',
        'selection_nationality_id',
        'nationality',
        'employment_name',
        'length_service_year',
        'length_service_month',
        'job_position',
        'selection_terms_of_employment_id',
        'terms_of_employment',
        'selection_occupation_id',
        'occupation',
        'selection_business_classification_id',
        'business_classification',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_id' => 'integer',
            'age' => 'integer',
            'birth_date' => 'date',
            'selection_gender_id' => 'integer',
            'selection_relationship_id' => 'integer',
            'selection_race_id' => 'integer',
            'selection_nationality_id' => 'integer',
            'length_service_year' => 'integer',
            'length_service_month' => 'integer',
            'selection_terms_of_employment_id' => 'integer',
            'selection_occupation_id' => 'integer',
            'selection_business_classification_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan that owns this guarantor.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id');
    }

    /**
     * Get the guarantor details for this guarantor.
     */
    public function loanGuarantorDetails(): HasMany
    {
        return $this->hasMany(LoanGuarantorDetail::class, 'loan_guarantor_id');
    }

    /**
     * Get the selection gender associated with this guarantor.
     */
    public function selectionGender(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_gender_id');
    }

    /**
     * Get the selection relationship associated with this guarantor.
     */
    public function selectionRelationship(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_relationship_id');
    }

    /**
     * Get the selection race associated with this guarantor.
     */
    public function selectionRace(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_race_id');
    }

    /**
     * Get the selection nationality associated with this guarantor.
     */
    public function selectionNationality(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_nationality_id');
    }

    /**
     * Get the selection terms of employment associated with this guarantor.
     */
    public function selectionTermsOfEmployment(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_terms_of_employment_id');
    }

    /**
     * Get the selection occupation associated with this guarantor.
     */
    public function selectionOccupation(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_occupation_id');
    }

    /**
     * Get the selection business classification associated with this guarantor.
     */
    public function selectionBusinessClassification(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_business_classification_id');
    }
}
