<?php

namespace App\Http\Requests\Collaterals;

use App\Enums\Collateral\CollateralStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateCollateralRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'selection_customer_type_id' => ['required', 'exists:selections,id'],
            'selection_type_id' => ['required', 'exists:selections,id'],
            'name' => ['required', 'string', 'max:255'],
            'identity_no' => ['required', 'string', 'max:20'],
            'status' => ['required', new Enum(CollateralStatus::class)],

            // Valuers
            'valuers' => ['required', 'array', 'min:1'],
            'valuers.*.valuation_amount' => ['required', 'numeric', 'min:0'],
            'valuers.*.valuer' => ['required', 'string', 'max:255'],
            'valuers.*.valuation_received_date' => ['required', 'date'],
            'valuers.*.land_search_received_date' => ['nullable', 'date'],
            'valuers.*.is_primary' => ['required', 'boolean'],

            // Property owners
            'property_owners' => ['nullable', 'array'],
            'property_owners.*.name' => ['required', 'string', 'max:255'],
            'property_owners.*.identity_no' => ['required', 'string', 'max:20'],
            'property_owners.*.telephone' => ['nullable', 'string', 'max:20'],
            'property_owners.*.mobile_phone' => ['nullable', 'string', 'max:20'],
            'property_owners.*.selection_telephone_country_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.selection_mobile_country_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.remark' => ['nullable', 'string', 'max:1000'],

            // Property owner addresses
            'property_owners.*.address.line_1' => ['nullable', 'string', 'max:255'],
            'property_owners.*.address.line_2' => ['nullable', 'string', 'max:255'],
            'property_owners.*.address.postcode' => ['nullable', 'string', 'max:10'],
            'property_owners.*.address.city' => ['nullable', 'string', 'max:50'],
            'property_owners.*.address.selection_state_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.address.state' => ['nullable', 'string', 'max:50'],
            'property_owners.*.address.selection_country_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.address.country' => ['nullable', 'string', 'max:50'],
        ];

        if ((int) $this->input('selection_customer_type_id') === 29) {
            $rules += [
                'company_name' => ['required', 'string'],
                'business_registration_no' => ['required', 'string', 'max:100'],
            ];
        }

        // Only include property.* rules if selection_type_id == 14
        if ((int) $this->input('selection_type_id') === 14) {
            $rules += [
                'property.ownership_no' => ['required', 'string', 'max:50'],
                'property.lot_number' => ['required', 'string', 'max:50'],
                'property.selection_land_category_id' => ['required', 'exists:selections,id'],
                'property.land_category' => ['nullable', 'string', 'max:50'],
                'property.land_category_other' => ['nullable', 'string', 'max:100'],
                'property.selection_type_of_property_id' => ['required', 'exists:selections,id'],
                'property.type_of_property' => ['nullable', 'string', 'max:50'],
                'property.land_size' => ['required', 'string', 'max:50'],
                'property.selection_land_size_unit' => ['required', 'exists:selections,id'],
                'property.land_size_unit' => ['nullable', 'string', 'max:50'],
                'property.selection_land_status_id' => ['required', 'exists:selections,id'],
                'property.land_status' => ['nullable', 'string', 'max:50'],
                'property.city' => ['required', 'string', 'max:50'],
                'property.location' => ['required', 'string'],
                'property.district' => ['required', 'string'],
                'property.no_syit_piawai' => ['required', 'string', 'max:50'],
                'property.certified_plan_no' => ['nullable', 'string', 'max:50'],
                'property.built_up_area_of_property' => ['nullable', 'string', 'max:50'],
                'property.selection_built_up_area_unit' => ['nullable', 'exists:selections,id'],
                'property.built_up_area_unit' => ['nullable', 'string', 'max:50'],

                // Address
                'property.address.line_1' => ['required', 'string', 'max:255'],
                'property.address.line_2' => ['nullable', 'string', 'max:255'],
                'property.address.postcode' => ['required', 'string', 'max:10'],
                'property.address.city' => ['required', 'string', 'max:50'],
                'property.address.selection_state_id' => ['required', 'exists:selections,id'],
                'property.address.state' => ['nullable', 'string', 'max:50'],
                'property.address.selection_country_id' => ['required', 'exists:selections,id'],
                'property.address.country' => ['nullable', 'string', 'max:50'],
            ];
        } else {
            $rules += [
                'remark' => ['required', 'string', 'max:1000'],
            ];
        }

        return $rules;
    }
}
