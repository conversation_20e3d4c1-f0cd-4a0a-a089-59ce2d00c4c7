<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_txns', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('code', 32)->unique();

            // Foreign key relationships
            $table->foreignId('loan_id')->constrained('loans')->cascadeOnDelete();
            $table->foreignId('loan_installment_id')->constrained('loan_installments')->cascadeOnDelete();
            $table->foreignId('loan_txn_type_id')->constrained('loan_txn_types')->cascadeOnDelete();
            $table->string('txn_type', 72)->nullable();

            // Charge information
            $table->decimal('amount', 24, 2);
            $table->unsignedTinyInteger('status');
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_txns');
    }
};
