import { useSweetAlert } from './useSweetAlert';

export function useFormSubmit() {
    const sweetAlert = useSweetAlert();

    const submitWithConfirmation = async <T extends Record<string, any>>(options: {
        form: any;
        confirmOptions?: {
            title?: string;
            html?: string;
            confirmText?: string;
            cancelText?: string;
        };
        submitOptions: {
            method: 'post' | 'put' | 'patch' | 'delete';
            url: string;
            data?: T;
            transform?: (data: any) => any;
            preserveScroll?: boolean;
            successMessage?: string;
            errorMessage?: string;
            resetForm?: boolean;
            entityName?: string;
        };
    }) => {
        const { form, submitOptions } = options;

        // Set defaults for confirmation options
        const confirmDefaults = {
            title: 'Are you sure?',
            html: `Do you want to ${getActionVerb(submitOptions.method)} this ${submitOptions.entityName || 'item'}?`,
            confirmText: `Yes, ${getActionVerb(submitOptions.method)} it!`,
            cancelText: 'Cancel',
        };

        const confirmOptions = { ...confirmDefaults, ...options.confirmOptions };

        // Set defaults for submit options
        const submitDefaults = {
            preserveScroll: true,
            resetForm: submitOptions.method === 'post', // Reset form by default only for POST
            successMessage: `${capitalize(submitOptions.entityName || 'Item')} has been ${getActionPastTense(submitOptions.method)} successfully!`,
            errorMessage: `Something went wrong while ${getActionVerbIng(submitOptions.method)} the ${submitOptions.entityName || 'item'}.`,
        };

        const mergedSubmitOptions = { ...submitDefaults, ...submitOptions };

        const confirmed = await sweetAlert.confirm(confirmOptions);
        if (!confirmed) return false;

        const submitData = {
            preserveScroll: mergedSubmitOptions.preserveScroll,
            onSuccess: () => {
                if (mergedSubmitOptions.resetForm) form.reset();
                sweetAlert.success(mergedSubmitOptions.successMessage, mergedSubmitOptions.method);
            },
            onError: () => {
                sweetAlert.error(mergedSubmitOptions.errorMessage, mergedSubmitOptions.method);
            },
        };

        if (mergedSubmitOptions.transform) {
            form.transform(mergedSubmitOptions.transform);
        }

        switch (mergedSubmitOptions.method) {
            case 'post':
                form.post(mergedSubmitOptions.url, submitData);
                break;
            case 'put':
                form.put(mergedSubmitOptions.url, submitData);
                break;
            case 'patch':
                form.patch(mergedSubmitOptions.url, submitData);
                break;
            case 'delete':
                form.delete(mergedSubmitOptions.url, submitData);
                break;
        }

        return true;
    };

    // Helper functions
    const getActionVerb = (method: string): string => {
        switch (method) {
            case 'post':
                return 'create';
            case 'put':
            case 'patch':
                return 'update';
            case 'delete':
                return 'delete';
            default:
                return 'submit';
        }
    };

    const getActionPastTense = (method: string): string => {
        switch (method) {
            case 'post':
                return 'created';
            case 'put':
            case 'patch':
                return 'updated';
            case 'delete':
                return 'deleted';
            default:
                return 'submitted';
        }
    };

    const getActionVerbIng = (method: string): string => {
        switch (method) {
            case 'post':
                return 'creating';
            case 'put':
            case 'patch':
                return 'updating';
            case 'delete':
                return 'deleting';
            default:
                return 'submitting';
        }
    };

    const capitalize = (str: string): string => {
        return str.charAt(0).toUpperCase() + str.slice(1);
    };

    return { submitWithConfirmation };
}
