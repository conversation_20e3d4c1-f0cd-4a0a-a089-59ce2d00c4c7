<?php

namespace App\Http\Requests\Loans;

use App\Enums\Loan\LoanStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateLoanRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_id' => ['required', 'integer', 'exists:customer_profiles,id'],
            'company_id' => ['required', 'integer', 'exists:companies,id'],
            'team_id' => ['required', 'integer', 'exists:teams,id'],
            'agent_id' => ['required', 'integer', 'exists:users,id'],
            'selection_type_id' => ['required', 'integer', 'exists:selections,id'],
            'status' => ['required', new Enum(LoanStatus::class)],
        ];
    }
}
