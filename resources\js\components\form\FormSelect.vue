<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';

interface Option {
    value: string | number;
    label: string;
}

interface Props {
    id: string;
    label: string;
    modelValue: string | number | (string | number)[];
    options: Option[];
    placeholder?: string;
    required?: boolean;
    error?: string;
    class?: string;
    labelClass?: string;
    selectClass?: string;
    multiple?: boolean;
    useMultiselect?: boolean;
    remark?: string;
    searchStatus?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Select an option',
    required: false,
    class: 'gap-3',
    labelClass: '',
    selectClass: '',
    multiple: false,
    useMultiselect: false,
    remark: '',
    searchStatus: '',
});

const emit = defineEmits(['update:modelValue']);

const handleChange = (value: string | number | (string | number)[]) => {
    emit('update:modelValue', value);
};
</script>

<template>
    <div :class="props.class">
        <Label :for="props.id" :class="props.labelClass">
            {{ props.label }}
            <RequiredIndicator v-if="props.required" />
        </Label>
        <div class="relative mt-1">
            <!-- Normal Select (UI components) -->
            <template v-if="!props.useMultiselect && !props.multiple">
                <div class="relative">
                    <Select :model-value="props.modelValue" @update:model-value="handleChange">
                        <SelectTrigger :id="props.id" :class="cn('w-full', props.selectClass)">
                            <SelectValue :placeholder="props.placeholder" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="option in props.options" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </SelectItem>
                        </SelectContent>
                    </Select>

                    <button
                        v-if="props.modelValue !== '' && props.modelValue !== undefined && props.modelValue !== null"
                        @click="handleChange('')"
                        class="absolute top-1.5 right-8 text-gray-500 hover:text-gray-700"
                        type="button"
                        aria-label="Clear selection"
                    >
                        <FaIcon name="xmark" class="h-4 w-4" />
                    </button>
                </div>
            </template>

            <!-- Multiselect (@vueform/multiselect) -->
            <template v-else>
                <Multiselect
                    :id="props.id"
                    :options="props.options"
                    :multiple="props.multiple"
                    :model-value="props.modelValue"
                    @update:model-value="handleChange"
                    searchable
                    :close-on-select="!props.multiple"
                    :clear-on-select="false"
                    :preserve-search="true"
                    :placeholder="props.placeholder"
                    :class="cn('w-full shadow-xs', props.selectClass)"
                    mode="tags"
                    :hideSelected="false"
                    :showOptions="true"
                    :canClear="false"
                    :canDeselect="props.multiple"
                    :openDirection="'bottom'"
                    :showLabels="false"
                    :valueProp="'value'"
                    :labelProp="'label'"
                >
                    <!-- Custom option template with checkbox style -->
                    <template #option="{ option, selected }">
                        <div class="flex items-center gap-2">
                            <div class="flex h-5 w-5 items-center justify-center">
                                <div v-if="selected" class="bg-primary flex h-4 w-4 items-center justify-center rounded-sm"></div>
                            </div>
                            <span>{{ option.label }}</span>
                        </div>
                    </template>
                </Multiselect>
            </template>
        </div>
        <p v-if="props.remark !== ''" class="text-muted-foreground text-sm">
            {{ props.remark }}
        </p>
        <InputError class="mt-1" :message="props.error" />
    </div>
</template>

<style>
.multiselect {
    min-height: 34px;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 14px;
    border-color: #e5e5e5;
}

.multiselect.is-active {
    box-shadow: 0 0 0 2px hsl(var(--ring));
}

.multiselect-placeholder {
    color: #737373;
}

.multiselect-wrapper {
    min-height: 34px;
}

.multiselect-tags {
    margin: 0rem 0.05rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.2rem;
}

.multiselect-tag {
    background-color: #f9f9f9;
    color: #0a0a0a;
    margin: 0.125rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.1rem;
    border: 1px solid #e2e8f0;
    font-weight: unset;
}

.multiselect-tag-remove {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #999999;
}

/* Checkbox styling for options */
.multiselect-option {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    cursor: pointer;
    gap: 0.5rem;
}

.multiselect-option.is-selected {
    background-color: unset;
    color: var(--color-black);
}

.multiselect-option.is-selected.is-pointed {
    background-color: #f3f4f6;
    color: var(--color-black);
}

.multiselect-option::before {
    content: '';
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    margin-right: 8px;
    background-color: white;
}

.multiselect-option.is-selected::before {
    background-color: #3b82f6;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white' width='18px' height='18px'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 12px;
    border-color: #3b82f6;
}

.multiselect-caret {
    width: 1rem;
    height: 1rem;
    background-color: #b9b9b9;
    -webkit-mask-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJtNiA5IDYgNiA2LTYiLz48L3N2Zz4=');
    mask-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJtNiA5IDYgNiA2LTYiLz48L3N2Zz4=');
}

.multiselect-caret.is-open {
    pointer-events: auto;
    transform: unset;
}
</style>
