@Library('library@0.0.1.X') _
// @Library('library@feature/DEVOPS-158') _

properties([
    parameters(
        projectEnvironmentParameter() +
        common.awsRegionParameter() +
        common.validatingVersionParameter()
    ) //end of parameters
]) //end of properties

projectPipeline = [
    environment: [
        PROJECT_CODE: "LAS",

        TASK_DEFINITION_JSON_FILE_PATH: "jenkins/aws/ecs/template-task-definition.json",
        ECS_CONFIG_JSON_FILE_PATH: "jenkins/aws/ecs/ecs-config.json",

        AWS_CLI_DEPLOYER_CREDENTIALS_ID_JSON: '''{
            "DEV":"LAS-DEV-DEPLOYER"
        }''',

        AWS_ACCOUNT_ID_JSON: '''{
            "DEV":"************"
        }''',

        AWS_S3_ACCESS_KEY_ID_JSON: '''{
            "DEV":"LAS-DEV-S3"
        }''',

        DOCKER_REGISTRY_USER_JSON: '''{
            "DEV":"AWS"
        }''',

        DOCKER_REGISTRY_URI_JSON : '''{
            "DEV":"************.dkr.ecr.ap-southeast-1.amazonaws.com"
        }''',

        APP_KEY_CREDENTIALS_ID_JSON: '''{
            "DEV":"LAS-DEV-APP-KEY"
        }''',

        DB_CREDENTIALS_ID_JSON: '''{
            "DEV":"LAS-DEV-DB"
        }'''
    ], //end of environment map
    stages: [
        preDeploy: {projectPreDeployStage()},
        deploy: {projectDeployStage()}
    ] //end of stages map
]

beLaravelDeployPipelineTemplate(projectPipeline)

def projectPreDeployStage() {
    log.debug "projectPreDeployStage() is called"

    env.AWS_ECS_CONTAINER_NAME = "$PROJECT_CODE_IN_LOWER_CASE-$ENVIRONMENT_IN_LOWER_CASE" //las-dev
    env.AWS_LOG_GROUP_NAME = "$PROJECT_CODE_IN_LOWER_CASE-$ENVIRONMENT_IN_LOWER_CASE" //las-dev
    env.TASK_DEFINITION_FAMILY_NAME = "$PROJECT_CODE_IN_LOWER_CASE-$ENVIRONMENT_IN_LOWER_CASE" //las-dev

    constructNewTaskDefinition()
} // end of projectPreDeployStage

def projectDeployStage() {
    log.debug "projectDeployStage() is called"

    env.AWS_ECS_CLUSTER_NAME = "$PROJECT_CODE-$ENVIRONMENT" //LAS-DEV
    env.AWS_ECS_SERVICE_NAME = "$PROJECT_CODE_IN_LOWER_CASE-$ENVIRONMENT_IN_LOWER_CASE" //las-dev

    withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: AWS_CLI_DEPLOYER_CREDENTIALS_ID]]) {
        registerEcsTaskDefinition()
        deployEcsService()
    }
} // end of projectDeployStage

List projectEnvironmentParameter() {
    return [
        [
            $class      : 'ChoiceParameter',
            choiceType  : 'PT_SINGLE_SELECT',
            description : 'Select the Environment from the Dropdown List',
            filterLength: 1,
            filterable  : false,
            name        : 'ENVIRONMENT',
            script      : [
                $class        : 'GroovyScript',
                fallbackScript: [
                    classpath : [],
                    sandbox   : true,
                    script    : "return['Could not get the projectEnvironmentParameter']"
                ],
                script       : [
                    classpath: [],
                    sandbox  : true,
                    script   : "return['DEV']"
                ]
            ]
        ]
    ]
} // end of projectEnvironmentParameter

//Pre-Deployment//
//////////////////////////constructNewTaskDefinition///////////////////////////
def constructNewTaskDefinition() {
    log.debug "Constucting new Task Definition ..."

    def taskDefinitionJson = readJSON file: TASK_DEFINITION_JSON_FILE_PATH

    // Step: Load ECS config entries
    def commonEcsConfigEntry = getConfigEntry(ECS_CONFIG_JSON_FILE_PATH,'commonDefault')
    def envCustomEcsConfigEntry = getConfigEntry(ECS_CONFIG_JSON_FILE_PATH,'Custom')

    // Step: Construct environment variables
    constructTaskEnvVars(commonEcsConfigEntry,envCustomEcsConfigEntry,taskDefinitionJson)

    // Step: Modifying image URL, containerPort, hostPort, cpu, and memory
    modifyContainerImageUrl(taskDefinitionJson)
    modifyContainerPortMappings(taskDefinitionJson,commonEcsConfigEntry,envCustomEcsConfigEntry)
    modifyTaskResources(taskDefinitionJson,commonEcsConfigEntry,envCustomEcsConfigEntry,"cpu")
    modifyTaskResources(taskDefinitionJson,commonEcsConfigEntry,envCustomEcsConfigEntry,"memory")

    // Step: Convert modified taskDefinitionJson object back into JSON string format
    def modifiedTaskDefinitionJson = groovy.json.JsonOutput.toJson(taskDefinitionJson)

    // Step: Construct a file path for the modified taskDefinitionJson including environment
    env.MODIFIED_TASK_DEFINITION_JSON_FILE_PATH = "jenkins/aws/ecs/$ENVIRONMENT_IN_LOWER_CASE-task-definition-modified.json"

    // Step: Write the JSON string (modifiedTaskDefinitionJson) into a new file path
    writeFile file: MODIFIED_TASK_DEFINITION_JSON_FILE_PATH, text: modifiedTaskDefinitionJson

    // Step: Replace placeholders with actual values
    executeShellCommand("""
        sed -i '
        s/\\\${CONTAINER_NAME}/$AWS_ECS_CONTAINER_NAME/g;
        s/\\\${AWS_LOG_GROUP_NAME}/$AWS_LOG_GROUP_NAME/g;
        s/\\\${AWS_REGION}/$AWS_REGION/g;
        s/\\\${TASK_DEFINITION_FAMILY_NAME}/$TASK_DEFINITION_FAMILY_NAME/g;
        s/\\\${AWS_ACCOUNT_ID}/$AWS_ACCOUNT_ID/g
        ' $MODIFIED_TASK_DEFINITION_JSON_FILE_PATH
    """)

    // Step: Read the contents of the new JSON file
    def finalTaskDefinitionJson = readJSON file: MODIFIED_TASK_DEFINITION_JSON_FILE_PATH
    log.debug "************ FINAL Task Definition JSON ************\n${finalTaskDefinitionJson.toString()}"
} // end of constructNewTaskDefinition

//////////////////////////constructTaskEnvVars///////////////////////////
def constructTaskEnvVars(Map commonEcsConfigEntry, Map customEcsConfigEntry, Map taskDefinitionJson) {
    log.debug "Constructing task environment variables ..."

    def commonEnv = commonEcsConfigEntry?.envVar ?: [:]
    def customEnv = customEcsConfigEntry?.envVar ?: [:]
    def commonSecretEnvInArray = commonEcsConfigEntry?.secretEnvVar?.split(',') ?: []

    if (!(commonEnv || customEnv || commonSecretEnvInArray)) {
        log.warning "No envVar and secretEnvVar defined in $ECS_CONFIG_JSON_FILE_PATH"
        return null
    }

    addTaskEnvVars(commonEnv,"Common environment variables",taskDefinitionJson)
    addTaskEnvVars(customEnv,"Project environment environment variables",taskDefinitionJson)

    // Project Environment Secret environment variables
    log.debug "Secret environment variables:\n ${commonSecretEnvInArray.join(', ')}"
    commonSecretEnvInArray.each { var ->
        switch (var) {
            case 'APP_KEY':
            log.debug "Adding $var"
                withCredentials([string(credentialsId: APP_KEY_CREDENTIALS_ID, variable: 'APP_KEY')]) {
                    taskDefinitionJson.containerDefinitions[0].environment += [[name: "APP_KEY", value: APP_KEY.toString()]]
                }
                break

            case 'DB_CREDENTIALS':
                log.debug "Adding $var, DB_USERNAME, and DB_PASSWORD"
                withCredentials([usernamePassword(credentialsId: DB_CREDENTIALS_ID, usernameVariable: 'DB_USERNAME', passwordVariable: 'DB_PASSWORD')]){

                    taskDefinitionJson.containerDefinitions[0].environment += [
                        [name: 'DB_USERNAME', value: DB_USERNAME],
                        [name: 'DB_PASSWORD', value: DB_PASSWORD.toString()]
                    ]
                }
                break

            case 'AWS_S3_ACCESS_KEY':
                log.debug "Adding $var, AWS_ACCESS_KEY_ID, and AWS_SECRET_ACCESS_KEY"
                withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: AWS_S3_ACCESS_KEY_ID, secretKeyVariable: 'AWS_S3_SECRET_KEY', accessKeyVariable: 'AWS_S3_ACCESS_KEY']]){

                    taskDefinitionJson.containerDefinitions[0].environment += [
                        [name: 'AWS_ACCESS_KEY_ID', value: AWS_S3_ACCESS_KEY],
                        [name: 'AWS_SECRET_ACCESS_KEY', value: AWS_S3_SECRET_KEY]
                    ]
                }
                break

            default:
                // Handle other cases if needed
                log.errorWithJobAbort """
                No handling specified for the environment variable '${var}'.
                Please check the switch cases to ensure that this environment variable is handled appropriately.
                """
        } // end of switch case
    } // end of combinedSecretEnvVarsInArray
    log.debug "************ CURRENT Task Definition JSON (constructTaskEnvVars Done) ************\n${taskDefinitionJson.toString()}"
} // end of constructTaskEnvVars

//////////////////////////addTaskEnvVars///////////////////////////
def addTaskEnvVars (Map envVarsMap, String envType, Map taskDefinitionJson) {
    log.debug "Adding $envType ..."

    log.debug "$envType:\n $envVarsMap"
    envVarsMap.each { key, value ->
        // If value is null or "null", assign empty string explicitly
        value = (value == null || value.toString().toLowerCase() == "null") ? "" : value.toString()
        log.debug "Adding $key"
        taskDefinitionJson.containerDefinitions[0].environment += [["name": key.toString(), "value": value]]
    }
} // end of addTaskEnvVars

//////////////////////////modifyContainerImageUrl///////////////////////////
def modifyContainerImageUrl(Map taskDefinitionJson){
    log.debug "Modifying image URL ..."

    def dockerImageUrl = "$DOCKER_REGISTRY_URI/$PROJECT_CODE_IN_LOWER_CASE:$VERSION"
    log.debug "dockerImageUrl: $dockerImageUrl"
    taskDefinitionJson.containerDefinitions[0].image = dockerImageUrl.toString()
} // end of modifyContainerImageUrl

//////////////////////////modifyContainerPortMappings///////////////////////////
def modifyContainerPortMappings(Map taskDefinitionJson, Map commonEntry, Map customEntry) {
    log.debug "Modifying containerPort and hostPort ..."

    def commonPort = commonEntry?.portMappings?.containerPort
    log.debug "commonPort: $commonPort"

    def customPort = customEntry?.portMappings?.containerPort
    log.debug "customPort: $customPort"

    def finalPort = customPort ?: commonPort
    if (finalPort) {
        taskDefinitionJson.containerDefinitions[0].portMappings[0].containerPort = finalPort.toInteger()
        taskDefinitionJson.containerDefinitions[0].portMappings[0].hostPort = finalPort.toInteger()
    } else {
        log.debug "No containerPort defined in config, will use default value in task definition template"
    }
} // end of modifyContainerPortMappings

//////////////////////////modifyTaskResources///////////////////////////
def modifyTaskResources(Map taskDefinitionJson, Map commonEntry, Map customEntry, String resourceType) {
    log.debug "Modifying $resourceType ..."

    def commonValue = commonEntry?.resources?."$resourceType"
    log.debug "common ${resourceType}: $commonValue"

    def customValue = customEntry?.resources?."$resourceType"
    log.debug "custom ${resourceType}: $customValue"

    def finalValue = customValue ?: commonValue
    if (finalValue) {
        taskDefinitionJson."$resourceType" = finalValue.toString()
    } else {
        log.debug "No $resourceType defined in config, will use default value in task definition template"
    }
} // end of modifyTaskResources

//Deployment//
//////////////////////////registerEcsTaskDefinition///////////////////////////
def registerEcsTaskDefinition() {
    log.debug "Registering modified $MODIFIED_TASK_DEFINITION_JSON_FILE_PATH as ECS task definition ..."

    def newTaskDefinitionArn = executeShellCommand("aws ecs register-task-definition --region $AWS_REGION --cli-input-json file://$MODIFIED_TASK_DEFINITION_JSON_FILE_PATH --query 'taskDefinition.taskDefinitionArn' --output text",true)

    // Extract latest task definition revision from newTaskDefinitionArn
    env.TASK_DEFINITION_LATEST_REVISION = newTaskDefinitionArn.tokenize(":").last()
    log.debug "TASK_DEFINITION_LATEST_REVISION: $TASK_DEFINITION_LATEST_REVISION"
} // end of registerEcsTaskDefinition

//////////////////////////deployEcsService///////////////////////////
def deployEcsService() {
    log.debug "Deploying ECS service ..."

    def commonEcsConfigEntry = getConfigEntry(ECS_CONFIG_JSON_FILE_PATH,'commonDefault')
    def envCustomEcsConfigEntry = getConfigEntry(ECS_CONFIG_JSON_FILE_PATH,'Custom')

    def expectedArn = "arn:aws:ecs:$AWS_REGION:$AWS_ACCOUNT_ID:service/$AWS_ECS_CLUSTER_NAME/$AWS_ECS_SERVICE_NAME"
    log.debug "expectedArn: ${expectedArn}"

    def serviceCheck = executeShellCommand("aws ecs list-services --cluster $AWS_ECS_CLUSTER_NAME --region $AWS_REGION --query \"serviceArns[?@ == '${expectedArn}']\" --output text",true)
    log.debug "serviceCheck result: ${serviceCheck}"

    serviceCheck == expectedArn ? updateEcsService(envCustomEcsConfigEntry) : createEcsService(commonEcsConfigEntry,envCustomEcsConfigEntry)
} // end of deployEcsService

//////////////////////////updateEcsService///////////////////////////
def updateEcsService(Map envCustomEcsConfigEntry) {
    log.debug "ECS Service $AWS_ECS_SERVICE_NAME found. Updating ..."

    def loadBalancerArgument = constructEcsCommandArgument("loadBalancer",envCustomEcsConfigEntry)

    executeShellCommand("""
        aws ecs update-service \
        --cluster $AWS_ECS_CLUSTER_NAME \
        --service $AWS_ECS_SERVICE_NAME \
        --task-definition $TASK_DEFINITION_FAMILY_NAME:$TASK_DEFINITION_LATEST_REVISION \
        --region $AWS_REGION \
        --desired-count $envCustomEcsConfigEntry.desiredCount \
        --force-new-deployment \
        $loadBalancerArgument
    """)
} // end of updateEcsService

//////////////////////////createEcsService///////////////////////////
def createEcsService(Map commonEcsConfigEntry, Map envCustomEcsConfigEntry) {
    log.debug "ECS Service $AWS_ECS_SERVICE_NAME not found. Creating ..."

    createEcsLogGroup()

    def capacityProviderStrategyArgument = constructEcsCommandArgument("capacityProviderStrategy",envCustomEcsConfigEntry)
    def placementStrategyArgument = constructEcsCommandArgument("placementStrategy",commonEcsConfigEntry)
    def placementConstraintsArgument = constructEcsCommandArgument("placementConstraints",commonEcsConfigEntry)
    def networkConfigurationArgument = constructEcsCommandArgument("networkConfiguration",commonEcsConfigEntry)
    def loadBalancerArgument = constructEcsCommandArgument("loadBalancer",envCustomEcsConfigEntry)

    log.debug "Creating ECS service for $AWS_ECS_SERVICE_NAME ..."
    executeShellCommand("""
        aws ecs create-service \
        --cluster $AWS_ECS_CLUSTER_NAME \
        --service-name $AWS_ECS_SERVICE_NAME \
        $capacityProviderStrategyArgument \
        --task-definition $TASK_DEFINITION_FAMILY_NAME:$TASK_DEFINITION_LATEST_REVISION \
        --region $AWS_REGION \
        --desired-count $envCustomEcsConfigEntry.desiredCount \
        --health-check-grace-period-seconds $envCustomEcsConfigEntry.healthCheckGracePeriodSeconds \
        $placementStrategyArgument \
        $placementConstraintsArgument \
        $networkConfigurationArgument \
        $loadBalancerArgument
    """)
} // end of createEcsService

//////////////////////////constructEcsCommandArgument///////////////////////////
def constructEcsCommandArgument(String argType, Map ecsConfigEntry) {
    log.debug "Constructing $argType argument in ECS command ..."
    def arg = ''
    // e.g., placementStrategy -> placement-Strategy -> --placement-strategy
    def cliFlag = "--" + argType.replaceAll(/([A-Z])/, '-$1').toLowerCase()

    switch (argType) {
        case 'capacityProviderStrategy':
            def cp = ecsConfigEntry?.capacityProvider
            if (!cp) { arg = ''; break }
            arg = "$cliFlag capacityProvider=$cp.name,weight=$cp.weight,base=$cp.base"
            break

            case 'placementStrategy':
                def strategies = ecsConfigEntry?.placementStrategy
                if (!strategies) return ''

                def jsonParts = strategies.collect { strategy ->
                    if (strategy.type && strategy.field) {
                        return """{"type":"${strategy.type}","field":"${strategy.field}"}"""
                    } else if (strat.type) {
                        return """{"type":"${strategy.type}"}"""
                    } else {
                        return null
                    }
                }.findAll { it != null }

                def jsonArray = "[${jsonParts.join(',')}]"
                arg = "$cliFlag '${jsonArray}'"
                break

            case 'placementConstraints':
                def constraints = ecsConfigEntry?.placementConstraints
                if (!constraints) return ''

                def jsonParts = constraints.collect { constraint ->
                    if (constraint.type == "memberOf" && constraint.expression) {
                        return """{"type":"${constraint.type}","expression":"${constraint.expression}"}"""
                    } else {
                        return """{"type":"${constraint.type}"}"""
                    }
                }

                def jsonArray = "[${jsonParts.join(',')}]"
                arg = "$cliFlag '${jsonArray}'"
                break

        case 'networkConfiguration':
            def networkConfig = ecsConfigEntry?.networkConfiguration
            if (ecsConfigEntry?.networkMode != "awsvpc" || !networkConfig.subnetIds) { arg = ''; break }
            arg = "$cliFlag \"awsvpcConfiguration={subnets=[$networkConfig.subnetIds],securityGroups=[$networkConfig.securityGroupIds]}\""
            break

        case 'loadBalancer':
            if (!ecsConfigEntry?.loadBalancerTargetGroupArn) { arg = ''; break }
            def port = executeShellCommand("aws ecs describe-task-definition --task-definition $TASK_DEFINITION_FAMILY_NAME --region $AWS_REGION | jq -r '.taskDefinition.containerDefinitions[0].portMappings[0].containerPort'",true)
            arg = "$cliFlag \"targetGroupArn=$ecsConfigEntry.loadBalancerTargetGroupArn,containerName=$AWS_ECS_CONTAINER_NAME,containerPort=$port\""
            break

        default:
            log.warn "Unknown ECS command argument type: $argType"
            return ''
    }

    log.debug "$argType argument:\n$arg"
    return arg
} // end of constructEcsCommandArgument

//////////////////////////createEcsLogGroup///////////////////////////
def createEcsLogGroup() {
    log.debug "Creating ECS log group ..."

    def logGroupCheck = executeShellCommand("aws logs describe-log-groups --query \"logGroups[?logGroupName == \'/ecs/$AWS_LOG_GROUP_NAME\']\" --output text",true)
    log.debug "logGroupCheck result: ${logGroupCheck}"
//
//     if (!logGroupCheck) {
//         log.debug "Log group $logGroupName not found. Creating ..."
//         // Step: Create log group with name $logGroupName
//         def createLogGroupCommand = """
//             aws logs create-log-group --log-group-name $logGroupName 2>/dev/null || echo "Log group already exists"
//         """
//         sh(returnStdout: true, script: createLogGroupCommand.trim())
//
//         def setRetentionDateCommand = """
//             aws logs put-retention-policy --log-group-name $logGroupName --retention-in-days 3
//         """
//         sh(returnStdout: true, script: setRetentionDateCommand.trim())
//     }
//
} // end of createEcsLogGroup

//////////////////////////getConfigEntry///////////////////////////
def getConfigEntry(String jsonFilePath, String entryType) {
    def jsonContent = readJSON(file: jsonFilePath)
    def entry

    switch (entryType) {
        case 'commonDefault':
            entry = jsonContent.containerDefinitions[0]?.commonDefault
            if (!entry) log.debug "No common default config found in ${jsonFilePath}"
            break
        case 'Custom':
            entry = jsonContent.containerDefinitions[0][ENVIRONMENT]?.Custom
            if (!entry) log.debug "No custom config found for '${ENVIRONMENT}' in ${jsonFilePath}"
            break
        default:
            log.warn "Unknown entry type '${entryType}' for ${jsonFilePath}"
            return null
    }
    return entry
} // end of getConfigEntry

//////////////////////////executeShellCommand///////////////////////////
def executeShellCommand(String command, boolean returnOutput = false) {
    def result = sh(script: command.trim(), returnStdout: returnOutput)
    return returnOutput ? result.trim() : null
} // end of executeShellCommand
